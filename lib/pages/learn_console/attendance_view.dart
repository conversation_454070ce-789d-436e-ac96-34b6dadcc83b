import 'dart:developer';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/mark_attendance_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/update_attendance_resp.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/learn_console/view_recording.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:provider/provider.dart';

enum StudentAttendenceStatus { none, present, absent, invited }

class AttendanceViewScreen extends StatefulWidget {
  final int? classId;
  final String? title;
  final String? batch;
  final String? classStatus;
  final String? startDate;
  final String? endDate;
  final String? recordUrl;

  const AttendanceViewScreen({
    super.key,
    this.classId,
    this.title,
    this.batch,
    this.classStatus,
    this.startDate,
    this.endDate,
    this.recordUrl,
  });

  @override
  State<AttendanceViewScreen> createState() => _AttendanceViewScreenState();
}

class _AttendanceViewScreenState extends State<AttendanceViewScreen> {
  TextEditingController searchController = TextEditingController();
  List<LiveClassUser>? liveClassUsers;
  Map<int, bool> isSelected = new Map<int, bool>();
  Map<int, StudentAttendenceStatus?> studentAttendenceStatus =
      new Map<int, StudentAttendenceStatus?>();

  bool? selectedVewAttendance = true;
  bool? selectedVewRecording = false;
  bool? isLoading = false;
  MarkAttendanceResponse? markAttendance;
  UpdateAttendanceResp? updateAttendance;

  bool? isAbsent = false;
  bool? isINV = false;
  int? selectedBatch;
  String? searchString;
  bool isAllChecked = false;
  bool? isMarkAttendance = false;

  bool isNotChecked = false;
  String? markUpdateButton;

  @override
  void initState() {
    getAttendanceView(
      classId: widget.classId,
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    log("your search value is ${searchString}");

    if (searchString != null) {
      liveClassUsers = liveClassUsers
          ?.where((element) =>
              element.name!.toLowerCase().contains(searchString!.toLowerCase()))
          .toList();
    }
    return BlocManager(
        initState: (context) {},
        child: Consumer<MenuListProvider>(
            builder: (context, mp, child) => BlocListener<HomeBloc, HomeState>(
                  listener: (context, state) async {
                    if (state is MarkAttendanceState) {
                      _handleMarkAttendanceState(state);
                    }

                    if (state is UpdateAttendanceState) {
                      _handleUpdateAttendance(state);
                    }
                  },
                  child: Scaffold(
                      appBar: AppBar(
                          elevation: 0,
                          backgroundColor: ColorConstants.WHITE,
                          leading: InkWell(
                              onTap: () {
                                Navigator.pop(context);
                              },
                              child: Icon(Icons.arrow_back,
                                  color: ColorConstants.BLACK)),
                          title: Text('Attendance View',
                              style: Styles.bold(size: 16))),
                      body: SingleChildScrollView(
                        child: Column(
                          children: [
                            attendanceView(),
                            SizedBox(height: 10),
                            attendanceList()
                          ],
                        ),
                      )),
                )));
  }

  batchFilter() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.05,
      width: MediaQuery.of(context).size.height * 0.2,
      margin: const EdgeInsets.symmetric(horizontal: 2, vertical: 8),
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
      decoration: ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
        shadows: const [
          BoxShadow(
            color: Color(0x26303E9F),
            blurRadius: 8,
            offset: Offset(0, 1),
            spreadRadius: 0,
          )
        ],
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton2<int>(
          isExpanded: true,
          hint: Row(
            children: [
              Expanded(
                child: Text(
                  'Batch',
                  style: Styles.regular(size: 14),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          items: markAttendance == null &&
                  markAttendance?.data?.batches?.isEmpty == true
              ? []
              : markAttendance?.data?.batches?.asMap().entries.map((entry) {
                    final index = entry.key;
                    final batch = entry.value;
                    return DropdownMenuItem<int>(
                      value: batch.id,
                      child: SizedBox(
                        width: MediaQuery.of(context).size.width * 0.7,
                        child: Text(batch.title!,
                            maxLines: 2,
                            style: const TextStyle(
                                color: Colors.black,
                                fontWeight: FontWeight.w500)),
                      ),
                    );
                  }).toList() ??
                  [],
          value: selectedBatch,
          onChanged: (int? value) {
            print('value is ${value}');

            setState(() {
              if (value != null) {
                getAttendanceView(classId: widget.classId, batchId: value);
                selectedBatch = value;
              }
            });
          },
        ),
      ),
    );
  }

  Widget attendanceView() {
    return Container(
      // height: MediaQuery.of(context).size.height * 0.30,
      margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      width: MediaQuery.of(context).size.width * 0.95,
      decoration: BoxDecoration(
          color: ColorConstants.WHITE, borderRadius: BorderRadius.circular(6)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(
                  width: 85,
                  height: 90,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: CachedNetworkImage(
                      imageUrl: '',
                      width: 100,
                      height: 120,
                      errorWidget: (context, url, error) => SvgPicture.asset(
                        'assets/images/gscore_postnow_bg.svg',
                      ),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                        width: width(context) * 0.38,
                        margin: EdgeInsets.only(left: 9, top: 3),
                        child: Text('${widget.title}',
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            softWrap: false,
                            style: Styles.semibold(size: 16))),
                    Container(
                        width: width(context) * 0.5,
                        margin: EdgeInsets.only(left: 9, top: 3),
                        child: Text(
                            'Lorem ipsum dolor sit amet consectetur. Leo magna scelerisque nisl molestie diam in morbi fermentum.',
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            softWrap: false,
                            style: Styles.regular(size: 12))),
                  ],
                ),
              ],
            ),
          ),
          Container(
              width: width(context),
              margin: EdgeInsets.only(left: 9, top: 3, right: 9),
              child: Row(
                children: [
                  Icon(Icons.calendar_month_outlined, size: 20),
                  SizedBox(width: 10),
                  SizedBox(
                    child: Text(
                      overflow: TextOverflow.ellipsis,
                      softWrap: false,
                      '${widget.endDate}',
                      style:
                          Styles.semibold(size: 12, color: Color(0xff0E1638)),
                      // textDirection: ui.TextDirection.ltr,
                    ),
                  ),
                  Spacer(),
                  Icon(Icons.alarm, size: 20),
                  SizedBox(width: 10),
                  SizedBox(
                    child: Text(
                      overflow: TextOverflow.ellipsis,
                      softWrap: false,
                      '${widget.startDate}',
                      style:
                          Styles.semibold(size: 12, color: Color(0xff0E1638)),
                      // textDirection: ui.TextDirection.ltr,
                    ),
                  ),
                ],
              )),
          // SizedBox(height: 30),
          // InkWell(
          //   onTap: () {
          //     setState(() {
          //       selectedVewAttendance = false;
          //       selectedVewRecording = true;
          //     });
          //   },
          //   child: Center(
          //     child: Container(
          //       height: height(context) * 0.05,
          //       width: width(context) * 0.6,
          //       decoration: BoxDecoration(
          //           color: selectedVewRecording == true
          //               ? Color(0xff3CA4D2)
          //               : ColorConstants.WHITE,
          //           borderRadius: BorderRadius.circular(10),
          //           border:
          //               Border.all(color: Color(0xffF0F1FA), width: 2)),
          //       margin: EdgeInsets.only(left: 9, top: 3),
          //       child: Padding(
          //         padding: const EdgeInsets.all(8.0),
          //         child: Center(
          //             child: Text('Upload Records',
          //                 style: Styles.textBold(
          //                     size: 12,
          //                     color: selectedVewRecording == true
          //                         ? ColorConstants.WHITE
          //                         : Color(0xff3CA4D2)))),
          //       ),
          //     ),
          //   ),
          // ),
          SizedBox(height: 20),
          if (widget.recordUrl != null)
            InkWell(
              onTap: () {
                setState(() {
                  selectedVewAttendance = true;
                  selectedVewRecording = false;
                });
                Navigator.push(
                    context,
                    NextPageRoute(
                        VideoPlayerScreen(recordUrl: widget.recordUrl)));
              },
              child: Center(
                child: Container(
                  height: height(context) * 0.05,
                  width: width(context) * 0.6,
                  decoration: BoxDecoration(
                    color: selectedVewAttendance == true
                        ? Color(0xff3CA4D2)
                        : ColorConstants.WHITE,
                    borderRadius: BorderRadius.circular(10),
                    // border:
                    //     Border.all(color: Color(0xffF0F1FA), width: 2)
                  ),
                  margin: EdgeInsets.only(left: 9, top: 3),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Center(
                        child: Text('view_recording',
                                style: Styles.textBold(
                                    size: 12,
                                    color: selectedVewAttendance == true
                                        ? ColorConstants.WHITE
                                        : Color(0xff3CA4D2)))
                            .tr()),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  List<Batch> get filteredBatches {
    final searchQuery = searchController.text.toLowerCase();
    return markAttendance?.data?.batches
            ?.where((batch) =>
                batch.title?.toLowerCase().contains(searchQuery) ?? false)
            .toList() ??
        [];
  }

  attendanceList() {
    return Container(
        // height: MediaQuery.of(context).size.height * 0.35,
        margin: EdgeInsets.symmetric(horizontal: 10),
        // width: MediaQuery.of(context).size.width * 0.95,
        decoration: BoxDecoration(
            color: Color(0xffF9FAFB), borderRadius: BorderRadius.circular(6)),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                batchFilter(),
                Spacer(),
                // if (searchString != null)
                //   SizedBox(width: 30, child: Text('$searchString')),
                SizedBox(
                  width: 130,
                  height: 40,
                  child: TextField(
                    cursorColor: Colors.grey,
                    controller: searchController,
                    onSubmitted: (value) {
                      setState(() {
                        searchString = value != '' ? value : null;
                      });
                    },
                    decoration: InputDecoration(
                      isDense: true,
                      fillColor: Colors.white,
                      filled: true,
                      border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide: BorderSide.none),
                      hintText: 'Search',
                      hintStyle: TextStyle(color: Colors.grey, fontSize: 18),
                      prefixIcon:
                          Icon(Icons.search, color: Colors.grey, size: 20),
                      suffixIcon: InkWell(
                          onTap: () {
                            searchString = null;
                            searchController.clear();
                          },
                          child: Icon(
                            Icons.close,
                            color: Colors.grey,
                            size: 20,
                          )),
                      contentPadding:
                          EdgeInsets.symmetric(vertical: 10.0, horizontal: 0),
                    ),
                  ),

                  // AnimSearchBar(
                  //   helpText: "Search...",
                  //   closeSearchOnSuffixTap: true,

                  //   rtl: true,
                  //   width: 500,
                  //   textController: searchController,
                  //   onSuffixTap: () {
                  //     setState(() {
                  //       print('suffix tap');
                  //        searchController.clear();
                  //       searchString = null;
                  //       // searchController.clear();
                  //     });
                  //   },
                  //   onSubmitted: (value) {
                  //     setState(() {
                  //       searchString = value != '' ? value : null;
                  //     });

                  //     // searchFilter(value);
                  //   },
                  // ),
                ),
                SizedBox(width: 10),
                InkWell(
                  onTap: () {
                    List<int> selectedUserId = [];
                    if (isAllChecked == true) {
                      liveClassUsers?.forEach((value) {
                        selectedUserId.add(value.userId!);
                      });
                    } else {
                      liveClassUsers?.forEach((value) {
                        if (isSelected[value.userId] == true) {
                          selectedUserId.add(value.userId!);
                        }
                      });
                    }
                    log("user ids are ${selectedUserId}");

                    showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return AlertDialog(
                            contentPadding: EdgeInsets.all(4),
                            content: SizedBox(
                              height: 200,
                              child: Stack(children: [
                                Column(
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        GestureDetector(
                                            onTap: () {
                                              Navigator.pop(context);
                                            },
                                            child: Icon(Icons.close, size: 20)),
                                      ],
                                    ),
                                    InkWell(
                                      onTap: () {
                                        for (var id in selectedUserId) {
                                          studentAttendenceStatus[id] =
                                              StudentAttendenceStatus.present;
                                        }

                                        getUpdateAttendance(
                                            contentId: widget.classId,
                                            attendance: 'present',
                                            users: selectedUserId);
                                        setState(() {
                                          isAbsent = false;
                                          isINV = false;
                                        });
                                        Navigator.pop(context);
                                      },
                                      child: Row(
                                        children: [
                                          Container(
                                            height: MediaQuery.of(context)
                                                    .size
                                                    .height *
                                                0.045,
                                            margin: EdgeInsets.symmetric(
                                                horizontal: 10, vertical: 10),
                                            width: MediaQuery.of(context)
                                                    .size
                                                    .width *
                                                0.10,
                                            decoration: BoxDecoration(
                                                color: Color.fromARGB(
                                                    255, 155, 189, 156),
                                                borderRadius:
                                                    BorderRadius.circular(100)),
                                            child: Center(
                                                child: Text('P',
                                                    style:
                                                        Styles.bold(size: 12))),
                                          ),
                                          Text("Student Present"),
                                        ],
                                      ),
                                    ),
                                    InkWell(
                                      onTap: () {
                                        for (var id in selectedUserId) {
                                          studentAttendenceStatus[id] =
                                              StudentAttendenceStatus.absent;
                                        }
                                        getUpdateAttendance(
                                            contentId: widget.classId,
                                            attendance: 'absent',
                                            users: selectedUserId);
                                        setState(() {
                                          isAbsent = true;

                                          isINV = false;
                                        });
                                        Navigator.pop(context);
                                      },
                                      child: Row(
                                        children: [
                                          Container(
                                            height: MediaQuery.of(context)
                                                    .size
                                                    .height *
                                                0.045,
                                            margin: EdgeInsets.symmetric(
                                                horizontal: 10, vertical: 10),
                                            width: MediaQuery.of(context)
                                                    .size
                                                    .width *
                                                0.10,
                                            decoration: BoxDecoration(
                                                color: ColorConstants.RED_BG,
                                                borderRadius:
                                                    BorderRadius.circular(100)),
                                            child: Center(
                                                child: Text('A',
                                                    style:
                                                        Styles.bold(size: 12))),
                                          ),
                                          Text("Student Absent"),
                                        ],
                                      ),
                                    ),
                                    InkWell(
                                      onTap: () {
                                        for (var id in selectedUserId) {
                                          studentAttendenceStatus[id] =
                                              StudentAttendenceStatus.invited;
                                        }
                                        print(
                                            'id is${selectedUserId.map((id) => id).toList()}');
                                        getUpdateAttendance(
                                            contentId: widget.classId,
                                            attendance: 'Invited',
                                            users: selectedUserId);
                                        setState(() {
                                          isINV = true;

                                          isAbsent = false;
                                        });
                                        Navigator.pop(context);
                                      },
                                      child: Row(
                                        children: [
                                          Container(
                                            height: MediaQuery.of(context)
                                                    .size
                                                    .height *
                                                0.045,
                                            margin: EdgeInsets.symmetric(
                                                horizontal: 10, vertical: 10),
                                            width: MediaQuery.of(context)
                                                    .size
                                                    .width *
                                                0.10,
                                            decoration: BoxDecoration(
                                                color: ColorConstants.GREY,
                                                borderRadius:
                                                    BorderRadius.circular(100)),
                                            child: Center(
                                                child: Text('INV',
                                                    style:
                                                        Styles.bold(size: 12))),
                                          ),
                                          Text("Student INV"),
                                        ],
                                      ),
                                    )
                                  ],
                                ),
                              ]),
                            ),
                          );
                        });
                  },
                  child: SvgPicture.asset(
                    'assets/images/filter.svg',
                    height: 15,
                    width: 15,
                    allowDrawingOutsideViewBox: true,
                  ),
                ),
              ],
            ),

            Divider(
              thickness: 1,
            ),
            Column(
              children: [
                Container(
                  color: Color(0xffEAECF0),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Row(
                      children: [
                        Checkbox(
                            activeColor: ColorConstants.ACTIVE_TAB_UNDERLINE,
                            value: isAllChecked,
                            onChanged: ((value) {
                              setState(() {
                                isAllChecked = value ?? false;
                              });
                            })),
                        Text('User ID', style: Styles.regular()),
                        Spacer(),
                        Text('Class Attendence'),
                        SizedBox(width: 5),
                        Icon(Icons.info_outline)
                      ],
                    ),
                  ),
                ),
                Divider(
                  thickness: 1,
                ),
                isLoading == true
                    ? Container(
                        margin: const EdgeInsets.only(top: 100),
                        height: 40,
                        width: 40,
                        child: CircularProgressIndicator(),
                      )
                    : liveClassUsers?.length == 0
                        ? SizedBox(
                            height: 200,
                            child: Center(child: Text('No User found')))
                        : ListView.builder(
                            physics: BouncingScrollPhysics(),
                            itemCount: liveClassUsers?.length ?? 0,
                            shrinkWrap: true,
                            itemBuilder: ((context, index) {
                              return Column(
                                children: [
                                  Container(
                                    // color: Color(0xffEAECF0),
                                    // height: 75,
                                    child: Column(
                                      children: [
                                        Row(
                                          children: [
                                            Checkbox(
                                              activeColor: ColorConstants
                                                  .ACTIVE_TAB_UNDERLINE,
                                              value: isAllChecked
                                                  ? true
                                                  : isSelected[
                                                          liveClassUsers![index]
                                                              .userId!] ??
                                                      false,
                                              onChanged: (value) {
                                                setState(() {
                                                  isSelected[int.parse(
                                                          '${liveClassUsers?[index].userId}')] =
                                                      value as bool;
                                                });
                                              },
                                            ),
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                    '${liveClassUsers?[index].name ?? ''}',
                                                    style:
                                                        Styles.bold(size: 12)),
                                                Text(
                                                    '${liveClassUsers?[index].email ?? ''}',
                                                    style: Styles.regular(
                                                        size: 12)),
                                              ],
                                            ),
                                            Spacer(),
                                            GestureDetector(
                                              onTap: () {
                                                StudentAttendenceStatus?
                                                    isPresent =
                                                    studentAttendenceStatus[
                                                        liveClassUsers?[index]
                                                            .userId];

                                                if (isPresent == null ||
                                                    isPresent ==
                                                        StudentAttendenceStatus
                                                            .none ||
                                                    isPresent ==
                                                        StudentAttendenceStatus
                                                            .absent ||
                                                    isPresent ==
                                                        StudentAttendenceStatus
                                                            .invited) {
                                                  studentAttendenceStatus[int.parse(
                                                          '${liveClassUsers?[index].userId}')] =
                                                      StudentAttendenceStatus
                                                          .present;

                                                  getUpdateAttendance(
                                                      contentId: widget.classId,
                                                      attendance: 'present',
                                                      users: [
                                                        liveClassUsers?[index]
                                                                .userId ??
                                                            0
                                                      ]);
                                                }

                                                setState(() {});
                                              },
                                              child: Container(
                                                height: MediaQuery.of(context)
                                                        .size
                                                        .height *
                                                    0.045,
                                                margin: EdgeInsets.symmetric(
                                                    horizontal: 10,
                                                    vertical: 10),
                                                width: MediaQuery.of(context)
                                                        .size
                                                        .width *
                                                    0.10,
                                                decoration: BoxDecoration(
                                                    // color: isPresent == true
                                                    color: studentAttendenceStatus[
                                                                liveClassUsers?[
                                                                        index]
                                                                    .userId] ==
                                                            StudentAttendenceStatus
                                                                .present
                                                        ? Color.fromARGB(
                                                            255, 122, 174, 122)
                                                        : ColorConstants.WHITE,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            100)),
                                                child: Center(
                                                    child: Text('P',
                                                        style: Styles.bold(
                                                            size: 12))),
                                              ),
                                            ),
                                            GestureDetector(
                                              onTap: () {
                                                StudentAttendenceStatus?
                                                    isPresent =
                                                    studentAttendenceStatus[
                                                        liveClassUsers?[index]
                                                            .userId];

                                                log("your value si ${isPresent}");
                                                if (isPresent == null ||
                                                    isPresent ==
                                                        StudentAttendenceStatus
                                                            .none ||
                                                    isPresent ==
                                                        StudentAttendenceStatus
                                                            .present ||
                                                    isPresent ==
                                                        StudentAttendenceStatus
                                                            .invited) {
                                                  studentAttendenceStatus[int.parse(
                                                          '${liveClassUsers?[index].userId}')] =
                                                      StudentAttendenceStatus
                                                          .absent;

                                                  getUpdateAttendance(
                                                      contentId: widget.classId,
                                                      attendance: 'absent',
                                                      users: [
                                                        liveClassUsers?[index]
                                                                .userId ??
                                                            0
                                                      ]);
                                                }

                                                setState(() {});
                                              },
                                              child: Container(
                                                height: MediaQuery.of(context)
                                                        .size
                                                        .height *
                                                    0.045,
                                                margin: EdgeInsets.symmetric(
                                                    horizontal: 4,
                                                    vertical: 10),
                                                width: MediaQuery.of(context)
                                                        .size
                                                        .width *
                                                    0.10,
                                                decoration: BoxDecoration(
                                                    color: studentAttendenceStatus[
                                                                    int.parse(
                                                                        '${liveClassUsers?[index].userId}')] ==
                                                                StudentAttendenceStatus
                                                                    .absent ||
                                                            isAbsent == true
                                                        ? Colors.red[300]
                                                        : ColorConstants.WHITE,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            100)),
                                                child: Center(
                                                    child: Text('A',
                                                        style: Styles.bold(
                                                            size: 12))),
                                              ),
                                            ),
                                            GestureDetector(
                                              onTap: () {
                                                StudentAttendenceStatus?
                                                    isPresent =
                                                    studentAttendenceStatus[
                                                        liveClassUsers?[index]
                                                            .userId];

                                                if (isPresent == null ||
                                                    isPresent ==
                                                        StudentAttendenceStatus
                                                            .none ||
                                                    isPresent ==
                                                        StudentAttendenceStatus
                                                            .absent ||
                                                    isPresent ==
                                                        StudentAttendenceStatus
                                                            .present) {
                                                  studentAttendenceStatus[int.parse(
                                                          '${liveClassUsers?[index].userId}')] =
                                                      StudentAttendenceStatus
                                                          .invited;

                                                  getUpdateAttendance(
                                                      contentId: widget.classId,
                                                      attendance: 'Invited',
                                                      users: [
                                                        liveClassUsers?[index]
                                                                .userId ??
                                                            0
                                                      ]);
                                                }
                                                // else if (isPresent ==
                                                //     StudentAttendenceStatus
                                                //         .invited) {
                                                //   studentAttendenceStatus[int.parse(
                                                //           '${liveClassUsers?[index].userId}')] =
                                                //       StudentAttendenceStatus
                                                //           .none;
                                                // }

                                                setState(() {});
                                              },
                                              child: Container(
                                                height: MediaQuery.of(context)
                                                        .size
                                                        .height *
                                                    0.045,
                                                margin: EdgeInsets.symmetric(
                                                    horizontal: 4,
                                                    vertical: 10),
                                                width: MediaQuery.of(context)
                                                        .size
                                                        .width *
                                                    0.10,
                                                decoration: BoxDecoration(
                                                    color: studentAttendenceStatus[
                                                                    int.parse(
                                                                        '${liveClassUsers?[index].userId}')] ==
                                                                StudentAttendenceStatus
                                                                    .invited ||
                                                            isINV == true
                                                        ? Colors.grey
                                                        : ColorConstants.WHITE,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            100)),
                                                child: Center(
                                                    child: Text('INV',
                                                        style: Styles.bold(
                                                            size: 12))),
                                              ),
                                            )
                                          ],
                                        ),
                                        Divider(thickness: 1)
                                      ],
                                    ),
                                  ),
                                ],
                              );
                            })),
              ],
            )
            // AttendanceList(
            //   liveClassUser: markAttendance?.data?.liveClassUser,
            //   searchString: searchString,
            // ),

            //     })),
          ],
        ));
  }

  void getUpdateAttendance(
      {int? contentId, String? attendance, required List<int> users}) {
    BlocProvider.of<HomeBloc>(context).add(UpdateAttendanceEvent(
        contentId: contentId, attendance: attendance, users: users));
  }

  void getAttendanceView({int? classId, int? batchId}) {
    BlocProvider.of<HomeBloc>(context)
        .add(MarkAttendanceEvent(classId: classId, batchId: batchId));
  }

  void _handleMarkAttendanceState(MarkAttendanceState state) {
    try {
      var loginState = state;
      setState(() {
        switch (loginState.apiState) {
          case ApiStatus.LOADING:
            Log.v("Loading...................FacultyBatchDetailsState.");
            isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            Log.v(
                "Success....................FacultyBatchDetailsState ${state.response?.data?.liveClassUser?.length}");

            markAttendance = state.response;
            liveClassUsers = state.response?.data?.liveClassUser;
            if (state.response?.data?.liveClassUser?.length != 0) {
              isMarkAttendance = true;
            }

            if (selectedBatch == null) {
              try {
                selectedBatch = state.response?.data?.batches?[0].id;
                getAttendanceView(
                    classId: widget.classId,
                    batchId: state.response?.data?.batches?[0].id);
              } catch (e) {}
            }

            for (var user in liveClassUsers!) {
              switch (user.status?.toLowerCase()) {
                case 'attended':
                  studentAttendenceStatus[int.parse('${user.userId}')] =
                      StudentAttendenceStatus.present;
                  break;
                case 'absent':
                  studentAttendenceStatus[int.parse('${user.userId}')] =
                      StudentAttendenceStatus.absent;
                  break;
                case 'invited':
                  studentAttendenceStatus[int.parse('${user.userId}')] =
                      StudentAttendenceStatus.invited;
                  break;
                default:
              }
            }

            isLoading = false;
            break;
          case ApiStatus.ERROR:
            Log.v("Error..........................FacultyBatchDetailsState");
            isLoading = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e, stacktrace) {
      Log.v("$stacktrace: $e");

      setState(() {
        isLoading = false;
      });
    }
  }

  void _handleUpdateAttendance(UpdateAttendanceState state) {
    try {
      var loginState = state;
      setState(() {
        switch (loginState.apiState) {
          case ApiStatus.LOADING:
            Log.v("Loading...................FacultyBatchDetailsState.");
            // isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            Log.v("Success....................FacultyBatchDetailsState");

            updateAttendance = state.response;

            // isLoading = false;
            break;
          case ApiStatus.ERROR:
            Log.v("Error..........................FacultyBatchDetailsState");
            // isLoading = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e, stacktrace) {
      Log.v("$stacktrace: $e");

      setState(() {
        // isLoading = false;
      });
    }
  }
}
